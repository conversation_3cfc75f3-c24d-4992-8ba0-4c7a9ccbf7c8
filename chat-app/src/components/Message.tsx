import React from 'react';
import { Message as MessageType } from '../types';

interface MessageProps {
  message: MessageType;
}

// Simple markdown renderer for basic formatting
const renderSimpleMarkdown = (text: string) => {
  const lines = text.split('\n');
  const elements: React.ReactNode[] = [];

  lines.forEach((line, index) => {
    if (line.startsWith('# ')) {
      elements.push(
        <h1 key={index} className="text-xl font-bold mb-2">
          {line.slice(2)}
        </h1>,
      );
    } else if (line.startsWith('## ')) {
      elements.push(
        <h2 key={index} className="text-lg font-bold mb-2">
          {line.slice(3)}
        </h2>,
      );
    } else if (line.startsWith('- ')) {
      elements.push(
        <li key={index} className="ml-4">
          {line.slice(2)}
        </li>,
      );
    } else if (line.includes('**') && line.includes('**')) {
      const parts = line.split('**');
      const formatted = parts.map((part, i) => (i % 2 === 1 ? <strong key={i}>{part}</strong> : part));
      elements.push(
        <p key={index} className="mb-1">
          {formatted}
        </p>,
      );
    } else if (line.includes('*') && line.includes('*')) {
      const parts = line.split('*');
      const formatted = parts.map((part, i) => (i % 2 === 1 ? <em key={i}>{part}</em> : part));
      elements.push(
        <p key={index} className="mb-1">
          {formatted}
        </p>,
      );
    } else if (line.trim() === '') {
      elements.push(<br key={index} />);
    } else {
      elements.push(
        <p key={index} className="mb-1">
          {line}
        </p>,
      );
    }
  });

  return <div>{elements}</div>;
};

export const Message: React.FC<MessageProps> = ({ message }) => {
  const renderContent = () => {
    // For system messages, render as plain text
    if (message.isSystem) {
      return <span>{message.content}</span>;
    }

    // For AI messages, render with simple markdown
    if (message.type === 'ai') {
      return renderSimpleMarkdown(message.content);
    }

    // For user messages, render as plain text
    return <span>{message.content}</span>;
  };

  return (
    <div className={`message ${message.type}-message ${message.isSystem ? 'system' : ''}`}>
      <div className="message-content">{renderContent()}</div>
      <div className="message-time">{message.timestamp}</div>
    </div>
  );
};
