require 'fast_mcp'
require 'json'
require 'webrick'
require 'rack'
require_relative 'flightlogger/client'
class ListAircraftTool < FastMcp::Tool
  description 'List all aircraft in the fleet'

  def call
    client = FlightLogger::Client.new
    aircraft_data = client.list_aircraft

    if aircraft_data[:success]
      {
        success: true,
        aircraft: aircraft_data[:aircraft],
        message: "Found #{aircraft_data[:aircraft].length} aircraft"
      }
    else
      {
        success: false,
        message: aircraft_data[:message] || 'Failed to retrieve aircraft data'
      }
    end
  end
end

class FindAircraftTool < FastMcp::Tool
  description 'Find a specific aircraft by name or search term'

  arguments do
    required(:search_term).filled(:string).description('Aircraft name or search term')
  end

  def call(search_term:)
    client = FlightLogger::Client.new
    aircraft_data = client.find_aircraft(search_term)

    if aircraft_data[:success]
      {
        success: true,
        aircraft: aircraft_data[:aircraft],
        message: "Found aircraft matching: #{search_term}"
      }
    else
      {
        success: false,
        message: aircraft_data[:message] || 'Aircraft not found'
      }
    end
  end
end

class CreateMaintenanceBookingTool < FastMcp::Tool
  description 'Create a maintenance booking for an aircraft with conflict checking'

  arguments do
    required(:aircraft_call_sign).filled(:string).description('Call sign of the aircraft')
    required(:start_time).filled(:string).description('Start time (ISO 8601 format)')
    required(:end_time).filled(:string).description('End time (ISO 8601 format)')
    optional(:description).filled(:string).description('Description of maintenance work')
    optional(:maintenance_type).filled(:string).description('Type of maintenance')
    optional(:check_conflicts).filled(:bool).description('Whether to check for conflicts (default: true)')
  end

  def call(aircraft_call_sign:, start_time:, end_time:, description: nil, maintenance_type: nil, check_conflicts: true)
    client = FlightLogger::Client.new

    # First find the aircraft to get its ID
    aircraft_data = client.find_aircraft(aircraft_call_sign)

    unless aircraft_data[:success] && aircraft_data[:aircraft]&.any?
      return {
        success: false,
        message: "Aircraft '#{aircraft_call_sign}' not found"
      }
    end

    aircraft = aircraft_data[:aircraft].first
    aircraft_id = aircraft['id']

    # Use conflict checking method if requested
    if check_conflicts
      result = client.create_maintenance_booking_with_conflict_check(
        aircraft_id: aircraft_id,
        booking_start: start_time,
        booking_end: end_time,
        comment: description || 'Scheduled maintenance',
        notify_via_email: false
      )
    else
      result = client.create_maintenance_booking(
        aircraft_id: aircraft_id,
        booking_start: start_time,
        booking_end: end_time,
        comment: description || 'Scheduled maintenance',
        notify_via_email: false
      )
    end

    if result[:success]
      {
        success: true,
        booking: result[:booking],
        aircraft: aircraft,
        message: "Maintenance booking created successfully for #{aircraft_call_sign}"
      }
    else
      {
        success: false,
        message: result[:message] || 'Failed to create maintenance booking'
      }
    end
  end
end

class CheckMaintenanceConflictsTool < FastMcp::Tool
  description 'Check for maintenance scheduling conflicts for a specific aircraft and time period'

  arguments do
    required(:aircraft_call_sign).filled(:string).description('Aircraft call sign to check')
    required(:start_time).filled(:string).description('Start time to check (ISO 8601 format)')
    required(:end_time).filled(:string).description('End time to check (ISO 8601 format)')
  end

  def call(aircraft_call_sign:, start_time:, end_time:)
    client = FlightLogger::Client.new

    # First find the aircraft to get its ID
    aircraft_data = client.find_aircraft(aircraft_call_sign)

    unless aircraft_data[:success] && aircraft_data[:aircraft]&.any?
      return {
        success: false,
        message: "Aircraft '#{aircraft_call_sign}' not found"
      }
    end

    aircraft = aircraft_data[:aircraft].first
    aircraft_id = aircraft['id']

    result = client.check_maintenance_conflicts(
      aircraft_id: aircraft_id,
      start_time: start_time,
      end_time: end_time
    )

    if result[:success]
      {
        success: true,
        conflicts: result[:conflicts] || [],
        aircraft: aircraft,
        message: result[:message]
      }
    else
      {
        success: false,
        message: result[:message] || 'Failed to check conflicts'
      }
    end
  end
end

class ListMaintenanceBookingsTool < FastMcp::Tool
  description 'List maintenance bookings for aircraft within a time period'

  arguments do
    optional(:aircraft_call_sign).filled(:string).description('Specific aircraft to list bookings for')
    optional(:from).filled(:string).description('Start date (ISO 8601 format)')
    optional(:to).filled(:string).description('End date (ISO 8601 format)')
  end

  def call(aircraft_call_sign: nil, from: nil, to: nil)
    client = FlightLogger::Client.new

    aircraft_id = nil
    if aircraft_call_sign
      # Find the aircraft to get its ID
      aircraft_data = client.find_aircraft(aircraft_call_sign)

      unless aircraft_data[:success] && aircraft_data[:aircraft]&.any?
        return {
          success: false,
          message: "Aircraft '#{aircraft_call_sign}' not found"
        }
      end

      aircraft_id = aircraft_data[:aircraft].first['id']
    end

    result = client.list_maintenance_bookings(
      aircraft_id: aircraft_id,
      from: from,
      to: to
    )

    if result[:success]
      {
        success: true,
        bookings: result[:bookings],
        message: "Found #{result[:bookings].length} maintenance bookings"
      }
    else
      {
        success: false,
        message: result[:message] || 'Failed to retrieve maintenance bookings'
      }
    end
  end
end

# FastMCP Resources
class AircraftFleetResource < FastMcp::Resource
  uri 'flightlogger://fleet/aircraft'
  resource_name 'Aircraft Fleet'
  description 'Current aircraft fleet information'
  mime_type 'application/json'

  def content
    client = FlightLogger::Client.new
    aircraft_data = client.list_aircraft

    if aircraft_data[:success]
      JSON.generate({
                      fleet_size: aircraft_data[:aircraft].length,
                      aircraft: aircraft_data[:aircraft],
                      last_updated: Time.now.iso8601
                    })
    else
      JSON.generate({
                      error: 'Failed to retrieve fleet data',
                      message: aircraft_data[:message]
                    })
    end
  end
end

class MaintenanceScheduleResource < FastMcp::Resource
  uri 'flightlogger://maintenance/schedule'
  resource_name 'Maintenance Schedule'
  description 'Current maintenance schedule and bookings'
  mime_type 'application/json'

  def content
    # For now, return mock data since we don't have a maintenance endpoint
    JSON.generate({
                    upcoming_maintenance: [],
                    active_bookings: [],
                    last_updated: Time.now.iso8601,
                    note: 'Maintenance scheduling system ready'
                  })
  end
end

# FastMCP Server
class FastMCPServer
  def self.create_server
    # Create FastMCP server
    server = FastMcp::Server.new(
      name: 'flightlogger-assistant',
      version: '1.0.0'
    )

    # Register tools
    server.register_tool(ListAircraftTool)
    server.register_tool(FindAircraftTool)
    server.register_tool(CreateMaintenanceBookingTool)
    server.register_tool(CheckMaintenanceConflictsTool)

    # Register resources
    server.register_resource(AircraftFleetResource)
    server.register_resource(MaintenanceScheduleResource)

    server
  end

  def self.start_stdio
    server = create_server

    puts 'Starting FastMCP FlightLogger Assistant (STDIO)'
    puts "Available tools: #{server.tools.length} tools registered"
    puts "Available resources: #{server.resources.length} resources registered"

    # Start STDIO transport
    server.start
  end

  def self.start_http
    server = create_server

    # Start the server with HTTP transport
    port = ENV.fetch('MCP_PORT', 8080).to_i
    host = ENV.fetch('MCP_HOST', '0.0.0.0')

    puts "Starting FastMCP FlightLogger Assistant (HTTP) on #{host}:#{port}"
    puts "Available tools: #{server.tools.length} tools registered"
    puts "Available resources: #{server.resources.length} resources registered"

    # Create a simple HTTP server that handles MCP requests directly
    webrick_server = WEBrick::HTTPServer.new(
      Port: port,
      Host: host,
      Logger: WEBrick::Log.new($stderr, WEBrick::Log::INFO),
      AccessLog: []
    )

    # Mount MCP endpoint that handles JSON-RPC requests
    webrick_server.mount_proc '/mcp' do |req, res|
      if req.request_method == 'POST'
        begin
          request_body = JSON.parse(req.body)

          case request_body['method']
          when 'tools/list'
            # Return static tool list for now
            tools_list = [
              {
                name: 'list_aircraft',
                description: 'List all aircraft in the fleet',
                inputSchema: {
                  type: 'object',
                  properties: {},
                  required: []
                }
              },
              {
                name: 'find_aircraft',
                description: 'Find aircraft by call sign',
                inputSchema: {
                  type: 'object',
                  properties: {
                    call_sign: { type: 'string', description: 'Aircraft call sign' }
                  },
                  required: ['call_sign']
                }
              },
              {
                name: 'create_maintenance_booking',
                description: 'Create a new maintenance booking for an aircraft',
                inputSchema: {
                  type: 'object',
                  properties: {
                    aircraft_id: { type: 'string', description: 'Aircraft identifier' },
                    booking_start: { type: 'string', description: 'Start time (ISO 8601)' },
                    booking_end: { type: 'string', description: 'End time (ISO 8601)' },
                    comment: { type: 'string', description: 'Booking comment' }
                  },
                  required: %w[aircraft_id booking_start booking_end]
                }
              },
              {
                name: 'check_maintenance_conflicts',
                description: 'Check for existing maintenance bookings in a date range',
                inputSchema: {
                  type: 'object',
                  properties: {},
                  required: []
                }
              }
            ]

            res.status = 200
            res['Content-Type'] = 'application/json'
            res.body = JSON.generate({
                                       jsonrpc: '2.0',
                                       id: request_body['id'],
                                       result: { tools: tools_list }
                                     })

          when 'tools/call'
            tool_name = request_body.dig('params', 'name')
            tool_args = request_body.dig('params', 'arguments') || {}

            # Find and call the appropriate tool with parameter mapping
            result = case tool_name
                     when 'list_aircraft'
                       ListAircraftTool.new.call
                     when 'find_aircraft'
                       FindAircraftTool.new.call(search_term: tool_args['call_sign'])
                     when 'create_maintenance_booking'
                       # Map parameters to match tool signature
                       CreateMaintenanceBookingTool.new.call(
                         aircraft_call_sign: tool_args['aircraft_id'],
                         start_time: tool_args['booking_start'],
                         end_time: tool_args['booking_end'],
                         description: tool_args['comment'],
                         maintenance_type: tool_args['maintenance_type']
                       )
                     when 'check_maintenance_conflicts'
                       CheckMaintenanceConflictsTool.new.call(
                         aircraft_call_sign: tool_args['aircraft_call_sign'],
                         start_time: tool_args['start_time'],
                         end_time: tool_args['end_time']
                       )
                     end

            if result
              res.status = 200
              res['Content-Type'] = 'application/json'
              res.body = JSON.generate({
                                         jsonrpc: '2.0',
                                         id: request_body['id'],
                                         result: result
                                       })
            else
              res.status = 400
              res['Content-Type'] = 'application/json'
              res.body = JSON.generate({
                                         jsonrpc: '2.0',
                                         id: request_body['id'],
                                         error: { code: -32_601, message: "Unknown tool: #{tool_name}" }
                                       })
            end

          else
            res.status = 400
            res['Content-Type'] = 'application/json'
            res.body = JSON.generate({
                                       jsonrpc: '2.0',
                                       id: request_body['id'],
                                       error: { code: -32_601, message: 'Method not found' }
                                     })
          end
        rescue StandardError => e
          res.status = 500
          res['Content-Type'] = 'application/json'
          res.body = JSON.generate({
                                     jsonrpc: '2.0',
                                     id: request_body&.dig('id'),
                                     error: { code: -32_603, message: 'Internal error', data: { error: e.message } }
                                   })
        end
      else
        res.status = 405
        res.body = 'Method Not Allowed'
      end
    end

    # Mount health endpoint
    webrick_server.mount_proc '/health' do |_req, res|
      res.status = 200
      res['Content-Type'] = 'application/json'
      res.body = JSON.generate({ status: 'healthy', timestamp: Time.now.iso8601 })
    end

    # Mount root endpoint
    webrick_server.mount_proc '/' do |_req, res|
      res.status = 200
      res['Content-Type'] = 'application/json'
      res.body = JSON.generate({
                                 name: 'FlightLogger MCP Assistant',
                                 version: '1.0.0',
                                 status: 'running',
                                 tools: server.tools.length,
                                 resources: server.resources.length,
                                 endpoints: {
                                   mcp: '/mcp',
                                   health: '/health'
                                 }
                               })
    end

    trap('INT') { webrick_server.shutdown }
    trap('TERM') { webrick_server.shutdown }

    puts 'FastMCP HTTP server started successfully!'
    puts "MCP endpoint: http://#{host}:#{port}/mcp"
    puts "Health endpoint: http://#{host}:#{port}/health"
    webrick_server.start
  end
end
