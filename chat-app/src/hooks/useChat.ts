import { useEffect, useRef, useState } from 'react';
import { MCPService, OllamaService } from '../services/api';
import { Message } from '../types';

export const useChat = () => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isConnected, setIsConnected] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement | null>(null);
  const initializationRef = useRef(false);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const addMessage = (type: Message['type'], content: string, isSystem = false) => {
    setMessages((prev) => [
      ...prev,
      {
        id: Date.now(),
        type,
        content,
        isSystem,
        timestamp: new Date().toLocaleTimeString(),
      },
    ]);
  };

  const initializeChat = async () => {
    if (initializationRef.current) return;
    initializationRef.current = true;

    try {
      addMessage('system', '🚀 Connecting to FlightLogger MCP Server and Ollama...', true);

      addMessage(
        'ai',
        '# Hello from FlightLogger Assistant!\n\nI can help you with:\n\n- **Aircraft Management**: View and search your fleet\n- **Maintenance Booking**: Schedule maintenance for aircraft\n- **Conflict Detection**: Check for scheduling conflicts\n\n*Ready to assist you!*',
      );

      await MCPService.listTools();

      const aircraftData = await MCPService.callTool('list_aircraft');

      if (aircraftData.success && aircraftData.aircraft) {
        setIsConnected(true);
        addMessage('system', '✅ Connected! You can now ask about your aircraft fleet.', true);
      } else {
        throw new Error('Failed to get aircraft data');
      }
    } catch (error) {
      addMessage('system', `❌ Error: ${(error as Error).message}`, true);
    }
  };

  const sendMessage = async (message: string) => {
    if (!message.trim() || isLoading) return;

    addMessage('user', message);
    setIsLoading(true);

    try {
      const availableTools = await MCPService.listTools();

      const systemPrompt = `You are the FlightLogger Assistant, an AI agent with access to aircraft management tools.

AVAILABLE MCP TOOLS:
${availableTools.map((tool) => `- ${tool.name}: ${tool.description}`).join('\n')}

INSTRUCTIONS:
1. When users ask about aircraft, maintenance, or scheduling, use the appropriate tools
2. For maintenance booking requests, use create_maintenance_booking tool
3. For aircraft information, use list_aircraft or find_aircraft tools
4. Always check for conflicts before booking maintenance
5. Respond naturally and helpfully based on tool results

USER REQUEST: ${message}`;

      let toolResults = '';

      try {
        const aircraftData = await MCPService.callTool('list_aircraft');
        if (aircraftData.success && aircraftData.aircraft) {
          toolResults += `\n[TOOL RESULT - list_aircraft]: Found ${aircraftData.aircraft.length} aircraft in fleet\n`;
          toolResults += aircraftData.aircraft.map((a: any) => `- ${a.callSign} (${a.model})`).join('\n') + '\n';
        }
      } catch (error) {
        toolResults += '\n[TOOL ERROR]: Could not retrieve fleet data\n';
      }

      const enhancedPrompt =
        systemPrompt +
        '\n\nTOOL EXECUTION RESULTS:\n' +
        toolResults +
        '\n\nBased on the above tool results, provide a helpful response to the user.';

      const aiResponse = await OllamaService.generate(enhancedPrompt);
      addMessage('ai', aiResponse);
    } catch (error) {
      addMessage('system', `❌ Error: ${(error as any).message}`, true);
    }

    setIsLoading(false);
  };

  return {
    messages,
    isLoading,
    isConnected,
    messagesEndRef,
    addMessage,
    initializeChat,
    sendMessage,
  };
};
