module.exports = {
  root: true,
  parser: '@typescript-eslint/parser',
  extends: [
    'eslint:recommended',
    'prettier',
    'plugin:@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:import/errors',
    'plugin:import/warnings',
  ],
  ignorePatterns: ['dist/', 'node_modules/', '*.config.js'],
  plugins: ['react', 'react-hooks', 'prettier', '@typescript-eslint'],
  settings: {
    react: {
      version: 'detect',
    },
  },
  env: {
    browser: true,
    es2020: true,
    node: true,
  },
  rules: {
    'no-case-declarations': 'warn',
    'comma-dangle': ['error', 'always-multiline'],
    'no-undef': 'off',
    'object-curly-spacing': ['warn', 'always'],
    'import/no-unresolved': [0, { commonjs: true }],
    'import/named': 0,
    'import/no-duplicates': 'off',
    'react/prop-types': 0,
    'react/no-deprecated': 2,
    'react/no-unsafe': [1, { checkAliases: true }],
    'react-hooks/rules-of-hooks': 'error',
    '@typescript-eslint/naming-convention': [
      'warn',
      {
        selector: 'default',
        format: ['camelCase', 'PascalCase', 'UPPER_CASE'],
        leadingUnderscore: 'allow',
      },
    ],
    '@typescript-eslint/no-inferrable-types': 'off',
    '@typescript-eslint/no-empty-function': 'off',
    '@typescript-eslint/explicit-function-return-type': 0,
    '@typescript-eslint/no-unused-vars': [
      'error',
      {
        ignoreRestSiblings: true,
      },
    ],
    '@typescript-eslint/ban-types': 'off',
    '@typescript-eslint/explicit-module-boundary-types': 'off',
    'prettier/prettier': 'error',
  },
};
