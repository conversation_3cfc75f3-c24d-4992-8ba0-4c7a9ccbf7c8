name: flightlogger_assistant

services:
  llm:
    image: ollama/ollama:latest
    container_name: llm
    ports:
      - "11434:11434"
    volumes:
      - llm_data:/root/.ollama
    environment:
      - OLLAMA_HOST=0.0.0.0
    restart: unless-stopped
    networks:
      - flightlogger_network
    healthcheck:
      test: ["CMD", "ollama", "list"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  mcp:
    build: ./mcp-server
    container_name: mcp
    ports:
      - "8080:8080"
    volumes:
      - ./mcp-server:/app
    environment:
      - FLIGHTLOGGER_API_KEY=${FLIGHTLOGGER_API_KEY}
      - FLIGHTLOGGER_BASE_URL=${FLIGHTLOGGER_BASE_URL}
    working_dir: /app
    command: ./docker-entrypoint.sh
    restart: unless-stopped
    networks:
      - flightlogger_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s

  chat:
    build: ./chat-app
    container_name: chat
    ports:
      - "3001:5173"
    volumes:
      - ./chat-app:/app
    environment:
      - NODE_ENV=development
    depends_on:
      mcp:
        condition: service_healthy
      llm:
        condition: service_healthy
    restart: unless-stopped
    networks:
      - flightlogger_network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5173"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

volumes:
  llm_data:

networks:
  flightlogger_network:
    external: true
