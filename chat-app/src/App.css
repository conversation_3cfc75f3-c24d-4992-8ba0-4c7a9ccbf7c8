* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

.app {
  height: 100vh;
  display: grid;
  grid-template-columns: 3fr 9fr;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Oxygen', 'Ubuntu', 'Cantarell', sans-serif;
  background:
    radial-gradient(ellipse at 30% 20%, rgba(255, 255, 255, 0.8) 0%, transparent 40%),
    radial-gradient(ellipse at 70% 60%, rgba(255, 255, 255, 0.6) 0%, transparent 35%),
    radial-gradient(ellipse at 20% 80%, rgba(255, 255, 255, 0.4) 0%, transparent 30%),
    radial-gradient(ellipse at 80% 30%, rgba(255, 255, 255, 0.5) 0%, transparent 25%),
    radial-gradient(ellipse at 50% 90%, rgba(255, 255, 255, 0.3) 0%, transparent 20%),
    linear-gradient(135deg, #87ceeb 0%, #b6e5ff 25%, #e0f6ff 50%, #f0f8ff 75%, #ffffff 100%);
  background-size:
    800px 400px,
    600px 300px,
    400px 200px,
    500px 250px,
    300px 150px,
    100% 100%;
  background-attachment: fixed;
  color: #1e293b;
  overflow: hidden;
  position: relative;
  animation: cloudDrift 30s ease-in-out infinite;
}

@keyframes cloudDrift {
  0%,
  100% {
    background-position:
      0% 0%,
      100% 100%,
      50% 50%,
      0% 0%,
      25% 75%,
      0% 0%;
  }
  25% {
    background-position:
      20% 10%,
      80% 90%,
      30% 70%,
      10% 20%,
      40% 60%,
      0% 0%;
  }
  50% {
    background-position:
      40% 20%,
      60% 80%,
      70% 30%,
      20% 40%,
      80% 20%,
      0% 0%;
  }
  75% {
    background-position:
      60% 30%,
      40% 70%,
      10% 90%,
      30% 60%,
      60% 40%,
      0% 0%;
  }
}

.sidebar {
  display: flex;
  flex-direction: column;
  background: rgba(255, 255, 255, 0.95);
  border-right: 1px solid rgba(59, 130, 246, 0.3);
  padding: 2rem 1.5rem;
  gap: 2rem;
  overflow-y: auto;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.brand-section {
  border-bottom: 1px solid rgba(59, 130, 246, 0.2);
  padding-bottom: 2rem;
}

.logo-container {
  display: flex;
  align-items: center;
  gap: 0.8rem;
  margin-bottom: 0.8rem;
}

.logo-icon {
  width: 2.5rem;
  height: 2.5rem;
  filter: drop-shadow(0 0 10px rgba(59, 130, 246, 0.3));
}

.brand-section h1 {
  color: #1e40af;
  font-size: 2.2rem;
  font-weight: 700;
  letter-spacing: -0.02em;
  margin: 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tagline {
  color: #1e40af;
  font-size: 1.1rem;
  font-weight: 600;
  letter-spacing: 0.02em;
  margin-bottom: 0.3rem;
  opacity: 0.8;
}

.subtitle {
  color: #475569;
  font-size: 0.85rem;
  font-weight: 400;
  letter-spacing: 0.02em;
  margin-bottom: 1.5rem;
  opacity: 0.8;
  line-height: 1.4;
}

.chat-history {
  flex: 1;
}

.chat-history h3 {
  color: #1e40af;
  font-size: 1rem;
  font-weight: 700;
  letter-spacing: 0.02em;
  margin-bottom: 1rem;
  opacity: 0.9;
}

.chat-list {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.chat-item {
  display: flex;
  flex-direction: column;
  padding: 1rem;
  background: rgba(59, 130, 246, 0.05);
  border: 1px solid rgba(59, 130, 246, 0.1);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.chat-item:hover {
  background: rgba(59, 130, 246, 0.1);
  border-color: rgba(59, 130, 246, 0.3);
  transform: translateX(4px);
}

.chat-item.active {
  background: rgba(59, 130, 246, 0.15);
  border-color: rgba(59, 130, 246, 0.4);
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.1);
}

.chat-title {
  color: #1e293b;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 0.3rem;
}

.chat-time {
  color: #64748b;
  font-size: 0.7rem;
  font-weight: 400;
  opacity: 0.7;
}

.status {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 6px;
  font-size: 0.7rem;
  font-weight: 700;
  letter-spacing: 0.1em;
  text-transform: uppercase;
  border: 1px solid;
}

.status-indicator.connected {
  background: rgba(34, 197, 94, 0.1);
  color: #22c55e;
  border-color: rgba(34, 197, 94, 0.3);
}

.status-indicator.disconnected {
  background: rgba(239, 68, 68, 0.1);
  color: #ef4444;
  border-color: rgba(239, 68, 68, 0.3);
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%,
  100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.chat-container {
  display: flex;
  flex-direction: column;
  margin: 2rem;
  background: rgba(255, 255, 255, 0.15);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 16px;
  box-shadow:
    0 20px 40px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  overflow: hidden;
}

.tools-info {
  background: linear-gradient(90deg, rgba(59, 130, 246, 0.08), rgba(59, 130, 246, 0.03));
  border-bottom: 1px solid rgba(59, 130, 246, 0.15);
  padding: 1rem 1.5rem;
  font-size: 0.85rem;
  display: flex;
  align-items: center;
  gap: 1rem;
  backdrop-filter: blur(10px);
}

.tools-label {
  color: #3b82f6;
  font-weight: 600;
  letter-spacing: 0.02em;
}

.tools-list {
  color: #94a3b8;
  font-weight: 500;
  letter-spacing: 0.02em;
}

.messages {
  flex: 1;
  padding: 2rem;
  overflow-y: auto;
  scroll-behavior: smooth;
  background: rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(5px);
}

.message {
  margin-bottom: 1.5rem;
  display: flex;
  flex-direction: column;
  max-width: 75%;
  animation: slideIn 0.4s ease-out;
}

.user-message {
  align-self: flex-end;
  margin-left: auto;
  width: fit-content;
  max-width: 100%;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.user-message .message-content {
  background: linear-gradient(135deg, #3b82f6, #60a5fa);
  color: #fff;
  padding: 1rem 1.5rem;
  border-radius: 20px 20px 5px 20px;
  box-shadow:
    0 4px 20px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  font-weight: 500;
  border: 1px solid rgba(59, 130, 246, 0.5);
}

.ai-message .message-content {
  background: rgba(255, 255, 255, 0.8);
  color: #1e293b;
  padding: 1rem 1.5rem;
  border-radius: 20px 20px 20px 5px;
  border: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow:
    0 4px 20px rgba(0, 0, 0, 0.1),
    inset 0 1px 0 rgba(255, 255, 255, 0.5);
  backdrop-filter: blur(10px);
}

.system .message-content {
  background: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
  padding: 0.75rem 1rem;
  border-radius: 12px;
  font-size: 0.9rem;
  text-align: center;
  max-width: 100%;
  align-self: center;
  border: 1px solid rgba(59, 130, 246, 0.3);
  font-weight: 500;
  letter-spacing: 0.02em;
}

.message-time {
  font-size: 0.75rem;
  color: #9ca3af;
  margin-top: 0.5rem;
  align-self: flex-end;
}

.user-message .message-time {
  align-self: flex-end;
}

.ai-message .message-time {
  align-self: flex-start;
}

.input-container {
  padding: 1.5rem 2rem;
  border-top: 1px solid rgba(59, 130, 246, 0.15);
  background: rgba(15, 23, 42, 0.2);
  backdrop-filter: blur(15px);
  display: flex;
  gap: 1rem;
  align-items: center;
}

.message-input {
  flex: 1;
  padding: 1rem 1.5rem;
  border: 1px solid rgba(59, 130, 246, 0.3);
  border-radius: 12px;
  font-size: 1rem;
  outline: none;
  transition: all 0.3s ease;
  background: rgba(30, 41, 59, 0.8);
  color: #e2e8f0;
  font-family: inherit;
}

.message-input::placeholder {
  color: #94a3b8;
  font-style: normal;
}

.message-input:focus {
  border-color: #3b82f6;
  box-shadow:
    0 0 0 2px rgba(59, 130, 246, 0.2),
    0 0 20px rgba(59, 130, 246, 0.1);
  background: rgba(30, 41, 59, 0.95);
}

.message-input:disabled {
  background: rgba(30, 41, 59, 0.5);
  color: #94a3b8;
  border-color: rgba(59, 130, 246, 0.1);
}

.send-button {
  padding: 1rem 2rem;
  background: linear-gradient(135deg, #3b82f6, #60a5fa);
  color: #fff;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-size: 1rem;
  font-weight: 600;
  letter-spacing: 0.02em;
  transition: all 0.3s ease;
  box-shadow:
    0 4px 20px rgba(59, 130, 246, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.2);
  border: 1px solid rgba(59, 130, 246, 0.5);
}

.send-button:hover:not(:disabled) {
  background: linear-gradient(135deg, #2563eb, #3b82f6);
  transform: translateY(-2px);
  box-shadow:
    0 6px 25px rgba(59, 130, 246, 0.4),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
}

.send-button:disabled {
  background: rgba(71, 85, 105, 0.5);
  color: #94a3b8;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  border-color: rgba(71, 85, 105, 0.3);
}

.loading-spinner {
  display: inline-block;
  width: 16px;
  height: 16px;
  border: 2px solid rgba(59, 130, 246, 0.2);
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 0.5rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* Scrollbar styling */
.messages::-webkit-scrollbar {
  width: 8px;
}

.messages::-webkit-scrollbar-track {
  background: rgba(15, 23, 42, 0.2);
  border-radius: 4px;
}

.messages::-webkit-scrollbar-thumb {
  background: rgba(59, 130, 246, 0.3);
  border-radius: 4px;
  border: 1px solid rgba(59, 130, 246, 0.1);
}

.messages::-webkit-scrollbar-thumb:hover {
  background: rgba(59, 130, 246, 0.5);
}

/* Markdown Styles */
.message-content .markdown-paragraph {
  margin: 0.5em 0;
  line-height: 1.6;
}

.message-content .markdown-paragraph:first-child {
  margin-top: 0;
}

.message-content .markdown-paragraph:last-child {
  margin-bottom: 0;
}

.message-content h1,
.message-content h2,
.message-content h3,
.message-content h4,
.message-content h5,
.message-content h6 {
  margin: 1em 0 0.5em 0;
  font-weight: 600;
  line-height: 1.4;
}

.message-content h1:first-child,
.message-content h2:first-child,
.message-content h3:first-child,
.message-content h4:first-child,
.message-content h5:first-child,
.message-content h6:first-child {
  margin-top: 0;
}

.message-content ul,
.message-content ol {
  margin: 0.5em 0;
  padding-left: 1.5em;
}

.message-content li {
  margin: 0.25em 0;
  line-height: 1.5;
}

.message-content blockquote {
  margin: 1em 0;
  padding: 0.5em 1em;
  border-left: 4px solid #e2e8f0;
  background: rgba(248, 250, 252, 0.5);
  font-style: italic;
}

.message-content .inline-code {
  background: rgba(99, 102, 241, 0.1);
  color: #4f46e5;
  padding: 0.125em 0.25em;
  border-radius: 3px;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 0.875em;
}

.message-content .code-block {
  background: rgba(15, 23, 42, 0.95);
  color: #e2e8f0;
  padding: 1em;
  border-radius: 8px;
  margin: 1em 0;
  overflow-x: auto;
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 0.875em;
  line-height: 1.5;
}

.message-content .code-block:first-child {
  margin-top: 0;
}

.message-content .code-block:last-child {
  margin-bottom: 0;
}

.message-content strong {
  font-weight: 600;
}

.message-content em {
  font-style: italic;
}

.message-content a {
  color: #3b82f6;
  text-decoration: underline;
}

.message-content a:hover {
  color: #1d4ed8;
}
